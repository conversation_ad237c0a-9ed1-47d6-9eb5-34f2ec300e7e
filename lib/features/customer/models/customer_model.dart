import 'package:json_annotation/json_annotation.dart'; // Optional, but manual from<PERSON>son used here

// Note: Using manual fromJson/toJson for simplicity. If using json_serializable, generate with build_runner.

class Customer {
  final String id;
  final String fullName;
  final String birthDay;
  final String phone;
  final String passport;
  final String jshshir;
  final String address;
  final Province? province;
  final Region? region;
  final DateTime createdAt;
  final DateTime updatedAt;

  Customer({
    required this.id,
    required this.fullName,
    required this.birthDay,
    required this.phone,
    required this.passport,
    required this.jshshir,
    required this.address,
    this.province,
    this.region,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Customer.fromJson(Map<String, dynamic> json) {
    return Customer(
      id: json['_id'] ?? '',
      fullName: json['fullName'] ?? '',
      birthDay: json['birthDay'] ?? '',
      phone: json['phone'] ?? '',
      passport: json['passport'] ?? '',
      jshshir: json['jshshir'] ?? '',
      address: json['address'] ?? '',
      province: json['province'] != null ? Province.fromJson(json['province']) : null,
      region: json['region'] != null ? Region.fromJson(json['region']) : null,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

}

class Province {
  final String id;
  final String title;
  final String desc;
  final DateTime createdAt;
  final DateTime updatedAt;

  Province({
    required this.id,
    required this.title,
    required this.desc,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Province.fromJson(Map<String, dynamic> json) {
    return Province(
      id: json['_id'] ?? '',
      title: json['title'] ?? '',
      desc: json['desc'] ?? '',
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'title': title,
      'desc': desc,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}

class Region {
  final String id;
  final String title;
  final String desc;
  final DateTime createdAt;
  final DateTime updatedAt;

  Region({
    required this.id,
    required this.title,
    required this.desc,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Region.fromJson(Map<String, dynamic> json) {
    return Region(
      id: json['_id'] ?? '',
      title: json['title'] ?? '',
      desc: json['desc'] ?? '',
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }
}



class PaginatedResponse<T> {
  final List<T> docs;
  final int totalDocs;
  final int limit;
  final int totalPages;
  final int page;
  final int pagingCounter;
  final bool hasPrevPage;
  final bool hasNextPage;
  final int? prevPage;
  final int? nextPage;

  PaginatedResponse({
    required this.docs,
    required this.totalDocs,
    required this.limit,
    required this.totalPages,
    required this.page,
    required this.pagingCounter,
    required this.hasPrevPage,
    required this.hasNextPage,
    this.prevPage,
    this.nextPage,
  });

  factory PaginatedResponse.fromJson(
      Map<String, dynamic> json,
      T Function(Map<String, dynamic>) fromJsonT,
      ) {
    return PaginatedResponse<T>(
      docs: (json['docs'] as List<dynamic>)
          .map((e) => fromJsonT(e as Map<String, dynamic>))
          .toList(),
      totalDocs: json['totalDocs'] ?? 0,
      limit: json['limit'] ?? 0,
      totalPages: json['totalPages'] ?? 0,
      page: json['page'] ?? 0,
      pagingCounter: json['pagingCounter'] ?? 0,
      hasPrevPage: json['hasPrevPage'] ?? false,
      hasNextPage: json['hasNextPage'] ?? false,
      prevPage: json['prevPage'],
      nextPage: json['nextPage'],
    );
  }
}
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mobil_kochat/core/theme/app_colors.dart';
import 'package:mobil_kochat/core/utils/app_constants.dart';

import '../../../../di/dependency_injection.dart';
import '../../models/customer_create_request.dart';
import '../../models/customer_model.dart';
import '../../services/customer_service.dart';

// Phone number formatter
class PhoneInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue,
      TextEditingValue newValue,
      ) {
    final text = newValue.text;

    // Remove all non-numeric characters
    final numericOnly = text.replaceAll(RegExp(r'[^\d]'), '');

    // Limit to 12 digits (998 + 9 digits)
    final limitedNumeric =
    numericOnly.length > 12 ? numericOnly.substring(0, 12) : numericOnly;

    String formatted = '';

    if (limitedNumeric.isNotEmpty) {
      // Add +998 prefix if not present
      if (!limitedNumeric.startsWith('998')) {
        if (limitedNumeric.length >= 1) {
          formatted = '+998 ';
          if (limitedNumeric.length >= 1) {
            formatted += limitedNumeric.substring(
                0, limitedNumeric.length > 2 ? 2 : limitedNumeric.length);
          }
          if (limitedNumeric.length > 2) {
            formatted +=
            ' ${limitedNumeric.substring(2, limitedNumeric.length > 5 ? 5 : limitedNumeric.length)}';
          }
          if (limitedNumeric.length > 5) {
            formatted +=
            '-${limitedNumeric.substring(5, limitedNumeric.length > 7 ? 7 : limitedNumeric.length)}';
          }
          if (limitedNumeric.length > 7) {
            formatted +=
            '-${limitedNumeric.substring(7, limitedNumeric.length > 9 ? 9 : limitedNumeric.length)}';
          }
        }
      } else {
        // Format with 998 prefix
        formatted = '+${limitedNumeric.substring(0, 3)}';
        if (limitedNumeric.length > 3) {
          formatted +=
          ' ${limitedNumeric.substring(3, limitedNumeric.length > 5 ? 5 : limitedNumeric.length)}';
        }
        if (limitedNumeric.length > 5) {
          formatted +=
          ' ${limitedNumeric.substring(5, limitedNumeric.length > 8 ? 8 : limitedNumeric.length)}';
        }
        if (limitedNumeric.length > 8) {
          formatted +=
          '-${limitedNumeric.substring(8, limitedNumeric.length > 10 ? 10 : limitedNumeric.length)}';
        }
        if (limitedNumeric.length > 10) {
          formatted += '-${limitedNumeric.substring(10, 12)}';
        }
      }
    }

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}

// JSHSHIR formatter
class JshshirInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue,
      TextEditingValue newValue,
      ) {
    final text = newValue.text;
    final numericOnly = text.replaceAll(RegExp(r'[^\d]'), '');
    final limitedNumeric =
    numericOnly.length > 14 ? numericOnly.substring(0, 14) : numericOnly;

    return TextEditingValue(
      text: limitedNumeric,
      selection: TextSelection.collapsed(offset: limitedNumeric.length),
    );
  }
}

// Passport formatter
class PassportInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue,
      TextEditingValue newValue,
      ) {
    String text = newValue.text.toUpperCase();
    text = text.replaceAll(RegExp(r'[^A-Z0-9]'), '');

    String formatted = '';

    if (text.length >= 1) {
      // First two characters should be letters
      final letters = text.substring(0, text.length > 2 ? 2 : text.length);
      formatted += letters.replaceAll(RegExp(r'[^A-Z]'), '');

      if (formatted.length == 2 && text.length > 2) {
        formatted += ' ';
        // Next 7 characters should be numbers
        final numbers = text.substring(2);
        final numericOnly = numbers.replaceAll(RegExp(r'[^\d]'), '');
        final limitedNumbers =
        numericOnly.length > 7 ? numericOnly.substring(0, 7) : numericOnly;
        formatted += limitedNumbers;
      }
    }

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}

class CustomerFormPage extends StatefulWidget {
  final Customer? customer;

  const CustomerFormPage({super.key, this.customer});

  @override
  State<CustomerFormPage> createState() => _CustomerFormPageState();
}

class _CustomerFormPageState extends State<CustomerFormPage> {
  final _formKey = GlobalKey<FormState>();
  final _customerService = CustomerService(
      dio: di(), // Your Dio instance,
      storage: di(), // Your GetStorage instance,
      networkInfo: di() // Your NetworkInfo instance,
  );

// Form controllers
  final _fullNameController = TextEditingController();
  final _birthDayController = TextEditingController();
  final _passportController = TextEditingController();
  final _phoneController = TextEditingController();
  final _jshshirController = TextEditingController();
  final _addressController = TextEditingController();

// Dropdown values
  Province? _selectedProvince;
  Region? _selectedRegion;

// Data lists
  List<Province> _provinces = [];
  List<Region> _regions = [];

// Loading states
  bool _isLoadingProvinces = true;
  bool _isLoadingRegions = false;
  bool _isCreating = false;

  bool get _isEdit => widget.customer != null;

  @override
  void initState() {
    super.initState();
    _initData();
  }

  Future<void> _initData() async {
    await _loadProvinces();
    if (_isEdit) {
      final cust = widget.customer!;
      _fullNameController.text = cust.fullName;
      _birthDayController.text = cust.birthDay ?? '';
      _passportController.text = _formatPassport(cust.passport);
      _phoneController.text = _formatPhone(cust.phone);
      _jshshirController.text = cust.jshshir ?? '';
      _addressController.text = cust.address;

      if (cust.province != null) {
        _selectedProvince = _provinces.firstWhere(
              (p) => p.id == cust.province,
          orElse: () => Province(id: '', title: '', desc: '', createdAt: DateTime.now(), updatedAt: DateTime.now()), // Placeholder if not found
        );
        if (_selectedProvince?.id != '') {
          await _onProvinceChanged(_selectedProvince);
          _selectedRegion = _regions.firstWhere(
                (r) => r.id == cust.region,
            orElse: () => Region(id: '', title: '', desc: '', createdAt: DateTime.now(), updatedAt: DateTime.now()), // Placeholder
          );
          if (_selectedRegion?.id == '') {
            _selectedRegion = null;
          }
        } else {
          _selectedProvince = null;
        }
      }
      setState(() {});
    } else {
      _phoneController.text = '+998 ';
    }
  }

  String _formatPhone(String rawPhone) {
    if (rawPhone.length != 9) return '+998 ';
    return '+998 ${rawPhone.substring(0, 2)} ${rawPhone.substring(2, 5)}-${rawPhone.substring(5, 7)}-${rawPhone.substring(7)}';
  }

  String _formatPassport(String rawPassport) {
    if (rawPassport.length != 9) return '';
    return '${rawPassport.substring(0, 2)} ${rawPassport.substring(2)}';
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _birthDayController.dispose();
    _passportController.dispose();
    _phoneController.dispose();
    _jshshirController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  Future<void> _loadProvinces() async {
    setState(() {
      _isLoadingProvinces = true;
    });

    try {
      final provinces = await _customerService.getProvinces();
      setState(() {
        _provinces = provinces;
        _isLoadingProvinces = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingProvinces = false;
      });
      if (mounted) {
        _showErrorSnackBar('Viloyatlarni yuklashda xato: $e');
      }
    }
  }

  Future<void> _onProvinceChanged(Province? province) async {
    setState(() {
      _selectedProvince = province;
      _selectedRegion = null;
      _regions = [];
      _isLoadingRegions = province != null;
    });

    if (province != null) {
      try {
        final regions = await _customerService.getRegions(
          provinceId: province.id,
        );
        setState(() {
          _regions = regions;
          _isLoadingRegions = false;
        });
      } catch (e) {
        setState(() {
          _isLoadingRegions = false;
        });
        if (mounted) {
          _showErrorSnackBar('Tuman/Shahar yuklashda xato: $e');
        }
      }
    }
  }

  void _onRegionChanged(Region? region) {
    setState(() {
      _selectedRegion = region;
    });
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now().subtract(const Duration(days: 365 * 25)),
      firstDate: DateTime(1950),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.cCardsColor,
              onPrimary: Colors.white,
            ),
          ),
          child: child!,
        );
      },
    );

    if (date != null) {
      _birthDayController.text =
      '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  String _getCleanPhone(String formattedPhone) {
    // Extract only numbers from formatted phone
    final numbers = formattedPhone.replaceAll(RegExp(r'[^\d]'), '');
    // Remove country code 998 and return 9-digit number
    if (numbers.startsWith('998') && numbers.length >= 12) {
      return numbers.substring(3);
    }
    return numbers.length >= 9
        ? numbers.substring(numbers.length - 9)
        : numbers;
  }

  String _getCleanPassport(String formattedPassport) {
    // Remove spaces from passport
    return formattedPassport.replaceAll(' ', '');
  }

  void _submitForm() async {
    if (_formKey.currentState!.validate()) {
      if (_selectedProvince == null || _selectedRegion == null) {
        _showErrorSnackBar('Viloyat va Tuman/Shahar tanlang');
        return;
      }

      setState(() {
        _isCreating = true;
      });

      try {
        final request = CustomerCreateRequest(
          fullName: _fullNameController.text.trim(),
          birthDay: _birthDayController.text.trim(),
          passport: _getCleanPassport(_passportController.text.trim()),
          phone: _getCleanPhone(_phoneController.text.trim()),
          jshshir: _jshshirController.text.trim(),
          address: _addressController.text.trim(),
          province: _selectedProvince!.id,
          region: _selectedRegion!.id,
        );

        bool success;
        if (_isEdit) {
          success = await _customerService.updateCustomer(widget.customer!.id, request);
        } else {
          success = await _customerService.createCustomer(request);
        }

        if (success) {
          _showSuccessSnackBar(_isEdit ? 'Mijoz muvaffaqiyatli yangilandi!' : 'Mijoz muvaffaqiyatli qo\'shildi!');
          Navigator.pop(context, true);
        } else {
          _showErrorSnackBar('Mijoz saqlashda xato yuz berdi');
        }
      } catch (e) {
        _showErrorSnackBar('Xato: $e');
      } finally {
        if (mounted) {
          setState(() {
            _isCreating = false;
          });
        }
      }
    }
  }

  Widget _buildCustomTextField({
    required TextEditingController controller,
    required String labelText,
    required IconData prefixIcon,
    String? hintText,
    TextInputType keyboardType = TextInputType.text,
    List<TextInputFormatter>? inputFormatters,
    String? Function(String?)? validator,
    bool readOnly = false,
    VoidCallback? onTap,
    Widget? suffixIcon,
    int maxLines = 1,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: TextFormField(
        controller: controller,
        decoration: InputDecoration(
          labelText: labelText,
          hintText: hintText,
          prefixIcon: Icon(prefixIcon, color: AppColors.cCardsColor),
          suffixIcon: suffixIcon,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: AppColors.cGreenishColor),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: AppColors.cGreenishColor),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: AppColors.cGreenishColor, width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: const BorderSide(color: Colors.red),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          filled: true,
          fillColor: Colors.grey.shade50,
          contentPadding:
          const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          labelStyle: TextStyle(color: Colors.grey.shade700),
        ),
        keyboardType: keyboardType,
        inputFormatters: inputFormatters,
        validator: validator,
        readOnly: readOnly,
        onTap: onTap,
        maxLines: maxLines,
        style: const TextStyle(fontSize: 16),
      ),
    );
  }

  Widget _buildCustomDropdown<T>({
    required String labelText,
    required IconData prefixIcon,
    required T? value,
    required List<T> items,
    required String Function(T) getTitle,
    required void Function(T?) onChanged,
    String? Function(T?)? validator,
    bool isLoading = false,
    bool enabled = true,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: DropdownButtonFormField<T>(
        value: value,
        decoration: InputDecoration(
          labelText: labelText,
          prefixIcon: Icon(prefixIcon, color: AppColors.cCardsColor),
          suffixIcon: isLoading
              ? const SizedBox(
            width: 20,
            height: 20,
            child: Padding(
              padding: EdgeInsets.all(12.0),
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: AppColors.cCardsColor, width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: const BorderSide(color: Colors.red),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          filled: true,
          fillColor: Colors.grey.shade50,
          contentPadding:
          const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          labelStyle: TextStyle(color: Colors.grey.shade700),
        ),
        items: items.map((item) {
          return DropdownMenuItem<T>(
            value: item,
            child: Text(
              getTitle(item),
              style: const TextStyle(fontSize: 16),
            ),
          );
        }).toList(),
        onChanged: enabled && !isLoading ? onChanged : null,
        validator: validator,
        dropdownColor: Colors.white,
        style: const TextStyle(color: Colors.black87, fontSize: 16),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          _isEdit ? 'Mijozni tahrirlash' : 'Yangi mijoz qo\'shish',
          style:  TextStyle(fontWeight: FontWeight.bold,color:AppColors.white),
        ),
        backgroundColor: AppColors.cGreenishColor,
        foregroundColor: Colors.white,
        elevation: 0,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(20),
          ),
        ),
      ),
      body: _isLoadingProvinces
          ? const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Ma\'lumotlar yuklanmoqda...'),
          ],
        ),
      )
          : SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Personal Information Section
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.shade200,
                      spreadRadius: 1,
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.person_outline,
                          color: AppColors.cCardsColor,
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          'Shaxsiy ma\'lumotlar',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),

                    // Full Name
                    _buildCustomTextField(
                      controller: _fullNameController,
                      labelText: 'To\'liq ismi *',
                      prefixIcon: Icons.person,
                      hintText: 'Familiya Ism Sharif',
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'To\'liq ismni kiriting';
                        }
                        if (value.trim().length < 3) {
                          return 'Ism juda qisqa';
                        }
                        return null;
                      },
                    ),

                    // Birth Date
                    _buildCustomTextField(
                      controller: _birthDayController,
                      labelText: 'Tug\'ilgan sana *',
                      prefixIcon: Icons.calendar_today,
                      hintText: 'YYYY-MM-DD',
                      readOnly: true,
                      onTap: _selectDate,
                      suffixIcon: const Icon(Icons.arrow_drop_down),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Tug\'ilgan sanani tanlang';
                        }
                        return null;
                      },
                    ),

                    // Passport
                    _buildCustomTextField(
                      controller: _passportController,
                      labelText: 'Passport seriyasi *',
                      prefixIcon: Icons.credit_card,
                      hintText: 'AB 1234567',
                      inputFormatters: [PassportInputFormatter()],
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Passport seriyasini kiriting';
                        }
                        final clean = value.replaceAll(' ', '');
                        if (clean.length != 9) {
                          return 'Passport formati: AB 1234567';
                        }
                        if (!RegExp(r'^[A-Z]{2}\d{7}$').hasMatch(clean)) {
                          return 'Passport formati noto\'g\'ri';
                        }
                        return null;
                      },
                    ),

                    // JSHSHIR
                    _buildCustomTextField(
                      controller: _jshshirController,
                      labelText: 'JSHSHIR *',
                      prefixIcon: Icons.fingerprint,
                      hintText: '12345678901234',
                      keyboardType: TextInputType.number,
                      inputFormatters: [JshshirInputFormatter()],
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'JSHSHIR ni kiriting';
                        }
                        if (value.length != 14) {
                          return 'JSHSHIR 14 ta raqamdan iborat bo\'lishi kerak';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Contact Information Section
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.shade200,
                      spreadRadius: 1,
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.contact_phone_outlined,
                          color: AppColors.cCardsColor,
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          'Aloqa ma\'lumotlari',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),

                    // Phone
                    _buildCustomTextField(
                      controller: _phoneController,
                      labelText: 'Telefon raqami *',
                      prefixIcon: Icons.phone,
                      hintText: '+998 99 123-45-67',
                      keyboardType: TextInputType.phone,
                      inputFormatters: [PhoneInputFormatter()],
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Telefon raqamini kiriting';
                        }
                        final numbers =
                        value.replaceAll(RegExp(r'[^\d]'), '');
                        if (numbers.length < 12) {
                          return 'To\'liq telefon raqamini kiriting';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Location Information Section
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.shade200,
                      spreadRadius: 1,
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.location_on_outlined,
                          color: AppColors.cCardsColor,
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          'Manzil ma\'lumotlari',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),

                    // Province
                    _buildCustomDropdown<Province>(
                      labelText: 'Viloyat *',
                      prefixIcon: Icons.location_city,
                      value: _selectedProvince,
                      items: _provinces,
                      getTitle: (province) => province.title,
                      onChanged: _onProvinceChanged,
                      validator: (value) {
                        if (value == null) {
                          return 'Viloyatni tanlang';
                        }
                        return null;
                      },
                    ),

                    // Region
                    _buildCustomDropdown<Region>(
                      labelText: 'Tuman/Shahar *',
                      prefixIcon: Icons.location_on,
                      value: _selectedRegion,
                      items: _regions,
                      getTitle: (region) => region.title,
                      onChanged: _onRegionChanged,
                      isLoading: _isLoadingRegions,
                      enabled: _selectedProvince != null,
                      validator: (value) {
                        if (value == null) {
                          return 'Tuman/Shahar tanlang';
                        }
                        return null;
                      },
                    ),

                    // Address
                    _buildCustomTextField(
                      controller: _addressController,
                      labelText: 'To\'liq manzil *',
                      prefixIcon: Icons.home,
                      hintText: 'Ko\'cha, uy raqami, xonadon...',
                      maxLines: 3,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'To\'liq manzilni kiriting';
                        }
                        if (value.trim().length < 10) {
                          return 'Manzil juda qisqa';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Submit button
              Container(
                height: 56,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: LinearGradient(
                    colors: _isCreating
                        ? [Colors.grey.shade400, Colors.grey.shade500]
                        : [
                      AppColors.cGreenishColor,
                      AppColors.cGreenishColor.withOpacity(0.8)
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  boxShadow: _isCreating
                      ? null
                      : [
                    BoxShadow(
                      color: AppColors.cCardsColor.withOpacity(0.3),
                      spreadRadius: 0,
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(cRadius22)),
                  child: InkWell(
                    onTap: _isCreating ? null : _submitForm,
                    borderRadius: BorderRadius.circular(16),
                    child: _isCreating
                        ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        ),
                        SizedBox(width: 12),
                        Text(
                          'Saqlanmoqda...',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    )
                        : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.save,
                          color: Colors.white,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _isEdit ? 'Yangilash' : 'Mijozni saqlash',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}
import 'package:flutter/material.dart';
import 'package:mobil_kochat/core/theme/app_colors.dart';
import 'package:mobil_kochat/core/utils/app_constants.dart';
import '../../../../di/dependency_injection.dart';
import '../../models/customer_model.dart';
import '../../services/customer_service.dart';
import 'costumer_add_page.dart';

class CustomerPage extends StatefulWidget {
  const CustomerPage({super.key});

  @override
  State<CustomerPage> createState() => _CustomerPageState();
}

class _CustomerPageState extends State<CustomerPage> {
  final CustomerService _customerService = CustomerService(
      dio: di(), // Your Dio instance,
      storage: di(), // Your GetStorage instance,
      networkInfo: di() // Your NetworkInfo instance,
  );

  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  List<Customer> _customers = [];
  bool _isLoading = false;
  bool _isSearching = false;
  bool _hasMore = true;
  int _currentPage = 1;
  String _lastSearchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadCustomers();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      _loadMoreCustomers();
    }
  }

  Future<void> _loadCustomers({bool refresh = false}) async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      if (refresh) {
        _customers.clear();
        _currentPage = 1;
        _hasMore = true;
      }
    });

    try {
      final response = await _customerService.getCustomers(
        page: _currentPage,
        limit: 20,
        search: _lastSearchQuery.isNotEmpty ? _lastSearchQuery : null,
        forceRefresh: refresh,
      );

      if (response != null) {
        setState(() {
          if (refresh || _currentPage == 1) {
            _customers = response.docs;
          } else {
            _customers.addAll(response.docs);
          }
          _hasMore = response.hasNextPage;
          if (_hasMore) {
            _currentPage = response.nextPage ?? _currentPage + 1;
          }
        });
      }
    } catch (e) {
      _showErrorSnackBar('Xatolik yuz berdi: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreCustomers() async {
    if (!_hasMore || _isLoading) return;
    await _loadCustomers();
  }

  Future<void> _searchCustomers(String query) async {
    if (_isSearching) return;

    setState(() {
      _isSearching = true;
      _lastSearchQuery = query.trim();
      _customers = [];
      _currentPage = 1;
      _hasMore = true;
    });

    try {
      await _loadCustomers(refresh: true);
    } catch (e) {
      _showErrorSnackBar('Qidirishda xatolik yuz berdi: $e');
    } finally {
      setState(() {
        _isSearching = false;
      });
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  Future<void> _refreshCustomers() async {
    await _loadCustomers(refresh: true);
  }

  Widget _buildCustomerCard(Customer customer) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          backgroundColor: AppColors.cCardsColor,
          child: Text(
            customer.fullName.isNotEmpty
                ? customer.fullName[0].toUpperCase()
                : 'M',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          customer.fullName,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text('📞 ${customer.phone}'),
            const SizedBox(height: 2),
            Text('📍 ${customer.address}'),
            if (customer.passport.isNotEmpty) ...[
              const SizedBox(height: 2),
              Text('🆔 ${customer.passport}'),
            ],
          ],
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => CustomerFormPage(customer: customer),
            ),
          );
          if (result == true) {
            _refreshCustomers();
          }
        },
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _lastSearchQuery.isNotEmpty
                ? 'Qidirilgan mijoz topilmadi'
                : 'Mijozlar ro\'yxati bo\'sh',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _lastSearchQuery.isNotEmpty
                ? 'Boshqa kalit so\'z bilan qidiring'
                : 'Yangi mijoz qo\'shish uchun "+" tugmasini bosing',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: FloatingActionButton.extended(
        shape: const StadiumBorder(),
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const CustomerFormPage()),
          );
          if (result == true) {
            _refreshCustomers();
          }
        },
        foregroundColor: Colors.white,
        backgroundColor: Colors.green,
        icon: const Icon(Icons.person_add),
        label: const Text(
          "Yangi mijoz",
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      body: Column(
        children: [
          // Search Header - Redesigned to be smaller and more modern
          Container(
            alignment: Alignment.center,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            width: double.infinity,
            height: 120, // Reduced height for better UX
            decoration: BoxDecoration(
              color: AppColors.cGreenishColor,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(cRadius36),
                bottomRight: Radius.circular(cRadius36),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: TextField(
              controller: _searchController,
              style: const TextStyle(color: Colors.white),
              onChanged: (value) {
                // Debounce search to avoid too many API calls
                Future.delayed(const Duration(milliseconds: 500), () {
                  if (_searchController.text == value) {
                    _searchCustomers(value);
                  }
                });
              },
              decoration: InputDecoration(
                hintText: 'Mijoz ism, telefon yoki passport bo\'yicha qidirish...',
                hintStyle: TextStyle(color: Colors.white.withOpacity(0.7), fontSize: 14),
                prefixIcon: _isSearching
                    ? Padding(
                  padding: const EdgeInsets.all(12),
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Colors.white.withOpacity(0.7),
                      ),
                    ),
                  ),
                )
                    : Icon(
                  Icons.search,
                  color: Colors.white.withOpacity(0.7),
                ),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                  icon: Icon(
                    Icons.clear,
                    color: Colors.white.withOpacity(0.7),
                  ),
                  onPressed: () {
                    _searchController.clear();
                    _searchCustomers('');
                  },
                )
                    : null,
                filled: true,
                fillColor: Colors.white.withOpacity(0.2),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(30), // More rounded
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 14,
                ),
              ),
            ),
          ),

          // Customer List
          Expanded(
            child: RefreshIndicator(
              onRefresh: _refreshCustomers,
              child: _customers.isEmpty
                  ? _isLoading
                  ? _buildLoadingIndicator()
                  : _buildEmptyState()
                  : ListView.builder(
                controller: _scrollController,
                itemCount: _customers.length + (_hasMore ? 1 : 0),
                itemBuilder: (context, index) {
                  if (index == _customers.length) {
                    return _buildLoadingIndicator();
                  }
                  return _buildCustomerCard(_customers[index]);
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
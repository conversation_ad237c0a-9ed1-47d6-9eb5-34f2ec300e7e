import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';
import '../../../core/network/network_info.dart';
import '../models/customer_model.dart';
import '../models/customer_create_request.dart';

// Import your NetworkInfo class, e.g.:
// import 'path/to/network_info.dart';

class CustomerService {
  final Dio dio;
  final GetStorage storage;
  final NetworkInfo networkInfo; // Assuming NetworkInfo is injected via DI

  CustomerService({
    required this.dio,
    required this.storage,
    required this.networkInfo,
  });

  Future<PaginatedResponse<Customer>?> getCustomers({
    required int page,
    required int limit,
    String? search,
    bool forceRefresh = false,
  }) async {
    // Basic caching example: Skip if not forceRefresh and use cache if available/recent
    // For full implementation, add timestamp checks, etc.
    if (!forceRefresh) {
      // Example cache key
      final cacheKey =
          'customers_page_${page}_limit_${limit}_search_${search ?? ''}';
      final cachedData = storage.read(cacheKey);
      if (cachedData != null) {
        // Assuming you store JSON string
        final jsonData = Map<String, dynamic>.from(jsonDecode(cachedData));
        return PaginatedResponse.fromJson(jsonData, Customer.fromJson);
      }
    }

    if (!await networkInfo.isConnected) {
      throw Exception('No internet connection');
    }

    try {
      String baseUrl = '/mobile/client/getAll';
      Map<String, dynamic> params = {
        'page': page,
        'limit': limit,
      };
      if (search != null && search.isNotEmpty) {
        params['search'] = search;
      }

      final String? token =
      storage.read('token'); // Assuming token stored as 'token'
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await dio.get(
        baseUrl,
        queryParameters: params,
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        final paginated = PaginatedResponse.fromJson(data, Customer.fromJson);

        // Cache the response (basic, without expiry check)
        final cacheKey =
            'customers_page_${page}_limit_${limit}_search_${search ?? ''}';
        storage.write(cacheKey, jsonEncode(data));

        return paginated;
      } else {
        throw Exception('Failed to load customers: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        // Handle token expiry, e.g., logout
        throw Exception('Authentication failed');
      }
      rethrow;
    } catch (e) {
      throw Exception('Error loading customers: $e');
    }
  }

  Future<List<Customer>> searchCustomers(String query) async {
    // Fetch with high limit to get all results (adjust as needed)
    final paginated = await getCustomers(
      page: 1,
      limit: 100, // High limit to simulate "all" results
      search: query,
      forceRefresh: true,
    );
    return paginated?.docs ?? [];
  }

  // New method: Get Provinces
  Future<List<Province>> getProvinces({bool forceRefresh = false}) async {
    if (!forceRefresh) {
      final cachedData = storage.read('provinces');
      if (cachedData != null) {
        final jsonData = jsonDecode(cachedData) as List;
        return jsonData.map((e) => Province.fromJson(e)).toList();
      }
    }

    if (!await networkInfo.isConnected) {
      throw Exception('No internet connection');
    }

    try {
      final String? token = storage.read('token');
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await dio.get(
        '/mobile/province/getAll',
        queryParameters: {'page': 1, 'limit': 100}, // Fetch all
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        final provinces = (data['docs'] as List)
            .map((e) => Province.fromJson(e))
            .toList();

        // Cache the response
        storage.write('provinces', jsonEncode(data['docs']));

        return provinces;
      } else {
        throw Exception('Failed to load provinces: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw Exception('Authentication failed');
      }
      rethrow;
    } catch (e) {
      throw Exception('Error loading provinces: $e');
    }
  }

  // New method: Get Regions by Province ID
  Future<List<Region>> getRegions({required String provinceId, bool forceRefresh = false}) async {
    final cacheKey = 'regions_province_${provinceId}';
    if (!forceRefresh) {
      final cachedData = storage.read(cacheKey);
      if (cachedData != null) {
        final jsonData = jsonDecode(cachedData) as List;
        return jsonData.map((e) => Region.fromJson(e)).toList();
      }
    }

    if (!await networkInfo.isConnected) {
      throw Exception('No internet connection');
    }

    try {
      final String? token = storage.read('token');
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await dio.get(
        '/mobile/region/getAll', // Assuming endpoint; adjust if different
        queryParameters: {'province': provinceId, 'page': 1, 'limit': 100},
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        final regions = (data['docs'] as List)
            .map((e) => Region.fromJson(e))
            .toList();

        // Cache the response
        storage.write(cacheKey, jsonEncode(data['docs']));

        return regions;
      } else {
        throw Exception('Failed to load regions: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw Exception('Authentication failed');
      }
      rethrow;
    } catch (e) {
      throw Exception('Error loading regions: $e');
    }
  }

  // New method: Create Customer
  Future<bool> createCustomer(CustomerCreateRequest request) async {
    if (!await networkInfo.isConnected) {
      throw Exception('No internet connection');
    }

    try {
      final String? token = storage.read('token');
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await dio.post(
        '/mobile/client', // Assuming endpoint based on pattern
        data: request.toJson(),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        ),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        // Invalidate customer cache if needed
        // For simplicity, we can remove a sample key; ideally, remove all customer caches
        storage.remove('customers_page_1_limit_20_search_');
        return true;
      } else {
        throw Exception('Failed to create customer: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw Exception('Authentication failed');
      }
      throw Exception('Error creating customer: ${e.message}');
    } catch (e) {
      throw Exception('Error creating customer: $e');
    }
  }

  // New method: Update Customer
  Future<bool> updateCustomer(String id, CustomerCreateRequest request) async {
    if (!await networkInfo.isConnected) {
      throw Exception('No internet connection');
    }

    try {
      final String? token = storage.read('token');
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await dio.put(
        '/mobile/client/$id', // Assuming endpoint with ID in path
        data: request.toJson(),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        ),
      );

      if (response.statusCode == 200) {
        // Invalidate customer cache if needed
        storage.remove('customers_page_1_limit_20_search_');
        return true;
      } else {
        throw Exception('Failed to update customer: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw Exception('Authentication failed');
      }
      throw Exception('Error updating customer: ${e.message}');
    } catch (e) {
      throw Exception('Error updating customer: $e');
    }
  }
}
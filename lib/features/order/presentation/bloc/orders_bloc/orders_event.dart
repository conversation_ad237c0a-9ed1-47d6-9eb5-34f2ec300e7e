part of 'orders_bloc.dart';

abstract class OrdersEvent {}

class LoadOrders extends OrdersEvent {
  final int page;
  final int limit;
  final String? status;
  final String? date;

  LoadOrders({
    this.page = 1,
    this.limit = 10,
    this.status,
    this.date,
  });
}

class RefreshOrders extends OrdersEvent {}

class LoadMoreOrders extends OrdersEvent {}

class FilterOrdersByStatus extends OrdersEvent {
  final String? status;

  FilterOrdersByStatus(this.status);
}

class FilterOrdersByDate extends OrdersEvent {
  final String? date;

  FilterOrdersByDate(this.date);
}

class CreateOrder extends OrdersEvent {
  final CreateOrderRequest request;

  CreateOrder(this.request);
}

// New events for location and data loading
class LoadProvinces extends OrdersEvent {}

class LoadRegions extends OrdersEvent {
  final String provinceId;

  LoadRegions(this.provinceId);
}

class LoadDistricts extends OrdersEvent {
  final String regionId;

  LoadDistricts(this.regionId);
}

class LoadTrees extends OrdersEvent {
  final String? variety;

  LoadTrees({this.variety});
}

class LoadClients extends OrdersEvent {
  final String? province;
  final String? region;
  final String? search;

  LoadClients({
    this.province,
    this.region,
    this.search,
  });
}

class LoadVarieties extends OrdersEvent {}

// Events for address selection
class SelectProvince extends OrdersEvent {
  final Province province;

  SelectProvince(this.province);
}

class SelectRegion extends OrdersEvent {
  final Region region;

  SelectRegion(this.region);
}

class SelectDistrict extends OrdersEvent {
  final District district;

  SelectDistrict(this.district);
}

// Event for clearing address selections
class ClearAddressSelection extends OrdersEvent {}
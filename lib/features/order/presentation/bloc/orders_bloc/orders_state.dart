// orders_state.dart
part of 'orders_bloc.dart';

class OrdersState {
  final bool isLoading;
  final bool isLoadingMore;
  final bool isCreating;
  final List<Order> orders;
  final String? error;
  final String? successMessage;
  final bool hasMore;
  final int currentPage;
  final int totalPages;
  final String? currentStatus;
  final String? currentDate;

  // New loading states
  final bool isLoadingProvinces;
  final bool isLoadingRegions;
  final bool isLoadingDistricts;
  final bool isLoadingTrees;
  final bool isLoadingClients;
  final bool isLoadingVarieties;

  // New data lists
  final List<Province>? provinces;
  final List<Region>? regions;
  final List<District>? districts;
  final List<Tree>? trees;
  final List<Customer>? clients;
  final List<TreeVariety>? varieties;

  // Selected address data
  final Province? selectedProvince;
  final Region? selectedRegion;
  final District? selectedDistrict;

  OrdersState({
    this.isLoading = false,
    this.isLoadingMore = false,
    this.isCreating = false,
    this.orders = const [],
    this.error,
    this.successMessage,
    this.hasMore = false,
    this.currentPage = 1,
    this.totalPages = 1,
    this.currentStatus,
    this.currentDate,
    // New loading states
    this.isLoadingProvinces = false,
    this.isLoadingRegions = false,
    this.isLoadingDistricts = false,
    this.isLoadingTrees = false,
    this.isLoadingClients = false,
    this.isLoadingVarieties = false,
    // New data lists
    this.provinces,
    this.regions,
    this.districts,
    this.trees,
    this.clients,
    this.varieties,
    // Selected address data
    this.selectedProvince,
    this.selectedRegion,
    this.selectedDistrict,
  });

  OrdersState copyWith({
    bool? isLoading,
    bool? isLoadingMore,
    bool? isCreating,
    List<Order>? orders,
    String? error,
    String? successMessage,
    bool? hasMore,
    int? currentPage,
    int? totalPages,
    String? currentStatus,
    String? currentDate,
    // New loading states
    bool? isLoadingProvinces,
    bool? isLoadingRegions,
    bool? isLoadingDistricts,
    bool? isLoadingTrees,
    bool? isLoadingClients,
    bool? isLoadingVarieties,
    // New data lists
    List<Province>? provinces,
    List<Region>? regions,
    List<District>? districts,
    List<Tree>? trees,
    List<Customer>? clients,
    List<TreeVariety>? varieties,
    // Selected address data
    Province? selectedProvince,
    Region? selectedRegion,
    District? selectedDistrict,
  }) {
    return OrdersState(
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      isCreating: isCreating ?? this.isCreating,
      orders: orders ?? this.orders,
      error: error,
      successMessage: successMessage,
      hasMore: hasMore ?? this.hasMore,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      currentStatus: currentStatus ?? this.currentStatus,
      currentDate: currentDate ?? this.currentDate,
      // New loading states
      isLoadingProvinces: isLoadingProvinces ?? this.isLoadingProvinces,
      isLoadingRegions: isLoadingRegions ?? this.isLoadingRegions,
      isLoadingDistricts: isLoadingDistricts ?? this.isLoadingDistricts,
      isLoadingTrees: isLoadingTrees ?? this.isLoadingTrees,
      isLoadingClients: isLoadingClients ?? this.isLoadingClients,
      isLoadingVarieties: isLoadingVarieties ?? this.isLoadingVarieties,
      // New data lists
      provinces: provinces ?? this.provinces,
      regions: regions ?? this.regions,
      districts: districts ?? this.districts,
      trees: trees ?? this.trees,
      clients: clients ?? this.clients,
      varieties: varieties ?? this.varieties,
      // Selected address data
      selectedProvince: selectedProvince ?? this.selectedProvince,
      selectedRegion: selectedRegion ?? this.selectedRegion,
      selectedDistrict: selectedDistrict ?? this.selectedDistrict,
    );
  }
}
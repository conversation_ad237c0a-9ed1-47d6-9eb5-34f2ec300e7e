import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:geolocator/geolocator.dart';
import 'package:image_picker/image_picker.dart';
import 'package:keyboard_dismisser/keyboard_dismisser.dart';
import 'package:mobil_kochat/core/theme/app_colors.dart';
import 'package:signature/signature.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:get_storage/get_storage.dart';
import 'dart:io';
import 'dart:convert';
import 'dart:typed_data';

import '../../../customer/models/customer_model.dart';
import '../../../customer/services/customer_service.dart';
import '../../../location/models/district_model.dart';

import '../../../tree/models/tree_variety_model.dart';
import '../../../../di/dependency_injection.dart' as di;

import '../../models/order_model.dart';
import '../bloc/orders_bloc/orders_bloc.dart';

class OrdersPage extends StatefulWidget {
  const OrdersPage({super.key});

  @override
  State<OrdersPage> createState() => _OrdersPageState();
}

class _OrdersPageState extends State<OrdersPage> {
  String? selectedStatus;
  String? selectedDate;

  @override
  void initState() {
    super.initState();
    context.read<OrdersBloc>().add(LoadOrders());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Buyurtmalar'),
        backgroundColor: Colors.green[600],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _showFilterBottomSheet,
            icon: const Icon(Icons.filter_list),
          ),
        ],
      ),
      body: KeyboardDismisser(
        child: BlocConsumer<OrdersBloc, OrdersState>(
          listener: (context, state) {
            if (state.error != null) {
              _showTopSnackBar(state.error!);
              context.read<OrdersBloc>().clearError();
            }

            if (state.successMessage != null) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.successMessage ?? '...',
                      style: const TextStyle(color: Colors.white)),
                  backgroundColor: Colors.green,
                  behavior: SnackBarBehavior.floating,
                  margin: EdgeInsets.only(
                    bottom: MediaQuery.of(context).size.height - 150,
                    left: 20,
                    right: 20,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              );
              context.read<OrdersBloc>().clearSuccessMessage();
            }
          },
          builder: (context, state) {
            if (state.isLoading && state.orders.isEmpty) {
              return const Center(child: CircularProgressIndicator());
            }

            if (state.orders.isEmpty) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.shopping_cart_outlined,
                        size: 64, color: Colors.grey),
                    SizedBox(height: 16),
                    Text(
                      'Buyurtmalar topilmadi',
                      style: TextStyle(fontSize: 18, color: Colors.grey),
                    ),
                  ],
                ),
              );
            }

            return RefreshIndicator(
              onRefresh: () async {
                context.read<OrdersBloc>().add(RefreshOrders());
              },
              child: NotificationListener<ScrollNotification>(
                onNotification: (ScrollNotification scrollInfo) {
                  if (!state.isLoadingMore &&
                      state.hasMore &&
                      scrollInfo.metrics.pixels ==
                          scrollInfo.metrics.maxScrollExtent) {
                    context.read<OrdersBloc>().add(LoadMoreOrders());
                  }
                  return false;
                },
                child: ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: state.orders.length + (state.hasMore ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == state.orders.length) {
                      return const Center(
                        child: Padding(
                          padding: EdgeInsets.all(16),
                          child: CircularProgressIndicator(),
                        ),
                      );
                    }

                    final order = state.orders[index];
                    return OrderCard(order: order, index: index);
                  },
                ),
              ),
            );
          },
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        onPressed: () => _showCreateOrderForm(context),
        backgroundColor: Colors.green[600],
        icon: const Icon(Icons.add, color: Colors.white),
        label: const Text('Buyurtma yaratish',
            style: TextStyle(color: Colors.white)),
      ),
    );
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      builder: (context) => FilterBottomSheet(
        selectedStatus: selectedStatus,
        selectedDate: selectedDate,
        onStatusChanged: (status) {
          setState(() => selectedStatus = status);
          context.read<OrdersBloc>().add(FilterOrdersByStatus(status));
        },
        onDateChanged: (date) {
          setState(() => selectedDate = date);
          context.read<OrdersBloc>().add(FilterOrdersByDate(date));
        },
      ),
    );
  }

  void _showCreateOrderForm(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CreateOrderPage(),
      ),
    );
  }

  void _showTopSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(color: Colors.white)),
        backgroundColor: Colors.red[600],
        duration: const Duration(seconds: 3),
      ),
    );
  }
}

class OrderCard extends StatelessWidget {
  final Order order;
  final int index;

  const OrderCard({super.key, required this.order, required this.index});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Thumbnail
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: order.photos.isNotEmpty &&
                          order.photos.first.isNotEmpty
                      ? CachedNetworkImage(
                          imageUrl: order.photos.first,
                          width: 60,
                          height: 60,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Container(
                            width: 60,
                            height: 60,
                            color: Colors.grey[300],
                            child: const Icon(Icons.image, color: Colors.grey),
                          ),
                          errorWidget: (context, url, error) => Container(
                            width: 60,
                            height: 60,
                            color: Colors.grey[300],
                            child: const Icon(Icons.image, color: Colors.grey),
                          ),
                        )
                      : Container(
                          width: 60,
                          height: 60,
                          color: Colors.grey[300],
                          child: const Icon(Icons.image, color: Colors.grey),
                        ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        order.client?.fullName ?? 'Mijoz #$index',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _formatDate(order.createdAt),
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${order.totalPrice.toStringAsFixed(0)} so\'m',
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 15,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  children: [
                    _buildStatusChip(order.status),
                    const SizedBox(height: 8),
                    if (order.pdf != null)
                      IconButton(
                        onPressed: () => _downloadContract(order.pdf!),
                        icon: const Icon(Icons.download, color: Colors.blue),
                      ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(int status) {
    Color color;
    String text;

    switch (status) {
      case 1:
        color = Colors.orange;
        text = 'Yangi';
        break;
      case 2:
        color = Colors.green;
        text = 'Yakunlangan';
        break;
      case 3:
        color = Colors.red;
        text = 'Bekor qilingan';
        break;
      default:
        color = Colors.grey;
        text = 'no-status';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}.${date.month.toString().padLeft(2, '0')}.${date.year}';
  }

  void _downloadContract(String contractUrl) {
    // Implement contract download functionality
    print('Downloading contract: $contractUrl');
  }
}

class FilterBottomSheet extends StatelessWidget {
  final String? selectedStatus;
  final String? selectedDate;
  final Function(String?) onStatusChanged;
  final Function(String?) onDateChanged;

  const FilterBottomSheet({
    super.key,
    this.selectedStatus,
    this.selectedDate,
    required this.onStatusChanged,
    required this.onDateChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Filtrlar',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          const Text('Status bo\'yicha:',
              style: TextStyle(fontWeight: FontWeight.w500)),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: [
              _buildFilterChip(
                  'Barchasi', null, selectedStatus, onStatusChanged),
              _buildFilterChip(
                  'Yangi', 'yangi', selectedStatus, onStatusChanged),
              _buildFilterChip('Yakunlangan', 'yakunlangan', selectedStatus,
                  onStatusChanged),
              _buildFilterChip('Bekor qilingan', 'bekor qilingan',
                  selectedStatus, onStatusChanged),
            ],
          ),
          const SizedBox(height: 16),
          const Text('Sana bo\'yicha:',
              style: TextStyle(fontWeight: FontWeight.w500)),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: DateTime.now(),
                      firstDate:
                          DateTime.now().subtract(const Duration(days: 365)),
                      lastDate: DateTime.now(),
                    );
                    if (date != null) {
                      onDateChanged(date.toIso8601String().split('T')[0]);
                    }
                  },
                  icon: const Icon(Icons.calendar_today),
                  label: Text(selectedDate ?? 'Sana tanlang'),
                ),
              ),
              const SizedBox(width: 8),
              if (selectedDate != null)
                IconButton(
                  onPressed: () => onDateChanged(null),
                  icon: const Icon(Icons.clear),
                ),
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String? value, String? selectedValue,
      Function(String?) onChanged) {
    final isSelected = selectedValue == value;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (_) => onChanged(value),
      selectedColor: Colors.green[100],
      checkmarkColor: Colors.green,
    );
  }
}

class CreateOrderPage extends StatefulWidget {
  const CreateOrderPage({super.key});

  @override
  State<CreateOrderPage> createState() => _CreateOrderPageState();
}

class _CreateOrderPageState extends State<CreateOrderPage> {
  final PageController _pageController = PageController();
  int _currentStep = 0;

  // Form data
  Customer? selectedClient;
  List<File> photos = [];
  Position? currentPosition;
  List<TreeProduct> treeProducts = [];
  PaymentType? paymentType;
  String? phoneNumber;
  String? smsCode;
  Uint8List? signature;
  File? contractFile;

  final SignatureController _signatureController = SignatureController(
    penStrokeWidth: 2,
    penColor: Colors.black,
    exportBackgroundColor: Colors.white,
  );

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}.${date.month.toString().padLeft(2, '0')}.${date.year}';
  }

  @override
  void initState() {
    super.initState();
    _getCurrentLocation();
    // Load initial data
    context.read<OrdersBloc>().add(LoadProvinces());
    context.read<OrdersBloc>().add(LoadVarieties());
    // Load user's default location preferences
    _loadUserLocationDefaults();
  }

  void _loadUserLocationDefaults() async {
    final storage = GetStorage();
    final defaultProvinceId = storage.read('user_default_province_id');
    final defaultRegionId = storage.read('user_default_region_id');
    final defaultDistrictId = storage.read('user_default_district_id');

    // Wait a bit for provinces to load, then set defaults
    Future.delayed(const Duration(milliseconds: 500), () {
      final state = context.read<OrdersBloc>().state;

      if (defaultProvinceId != null && state.provinces != null) {
        final defaultProvince = state.provinces!.firstWhere(
          (p) => p.id == defaultProvinceId,
          orElse: () => state.provinces!.first,
        );
        context.read<OrdersBloc>().add(SelectProvince(defaultProvince));

        // Set region default after province is selected
        if (defaultRegionId != null) {
          Future.delayed(const Duration(milliseconds: 300), () {
            final updatedState = context.read<OrdersBloc>().state;
            if (updatedState.regions != null &&
                updatedState.regions!.isNotEmpty) {
              Region? defaultRegion;
              try {
                defaultRegion = updatedState.regions!.firstWhere(
                  (r) => r.id == defaultRegionId,
                );
              } catch (e) {
                defaultRegion = updatedState.regions!.first;
              }

              context.read<OrdersBloc>().add(SelectRegion(defaultRegion));

              // Set district default after region is selected
              if (defaultDistrictId != null) {
                Future.delayed(const Duration(milliseconds: 300), () {
                  final finalState = context.read<OrdersBloc>().state;
                  if (finalState.districts != null &&
                      finalState.districts!.isNotEmpty) {
                    District? defaultDistrict;
                    try {
                      defaultDistrict = finalState.districts!.firstWhere(
                        (d) => d.id == defaultDistrictId,
                      );
                    } catch (e) {
                      defaultDistrict = finalState.districts!.first;
                    }

                    if (defaultDistrict != null) {
                      context
                          .read<OrdersBloc>()
                          .add(SelectDistrict(defaultDistrict));
                    }
                  }
                });
              }
                        }
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Yangi buyurtma'),
        backgroundColor: Colors.green[600],
        foregroundColor: Colors.white,
      ),
      body: BlocConsumer<OrdersBloc, OrdersState>(
        listener: (context, state) {
          if (state.error != null) {
            _showTopSnackBar(state.error!);
          }
          if (state.successMessage != null) {
            Navigator.pop(context);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.successMessage ?? '...',
                    style: const TextStyle(color: Colors.white)),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
                margin: EdgeInsets.only(
                  bottom: MediaQuery.of(context).size.height - 150,
                  left: 20,
                  right: 20,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            );
          }
        },
        builder: (context, state) {
          return Column(
            children: [
              // Progress indicator
              LinearProgressIndicator(
                value: (_currentStep + 1) / 6,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(Colors.green[600]!),
              ),
              Expanded(
                child: PageView(
                  controller: _pageController,
                  onPageChanged: (index) =>
                      setState(() => _currentStep = index),
                  children: [
                    _buildClientSelection(),
                    _buildPhotoCapture(),
                    _buildTreeSelection(),
                    _buildPaymentSelection(),
                    _buildSignatureCapture(),
                    _buildOrderSummary(),
                  ],
                ),
              ),
              _buildNavigationButtons(state),
            ],
          );
        },
      ),
    );
  }

  Widget _buildClientSelection() {
    return BlocBuilder<OrdersBloc, OrdersState>(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Mijoz va joylashuv ma\'lumotlari',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),

              // Client Selection Card
              Card(
                child: ListTile(
                  leading: const Icon(Icons.person, color: Colors.green),
                  title: Text(selectedClient?.fullName ?? 'Mijoz tanlang'),
                  subtitle: selectedClient != null
                      ? Text(selectedClient!.phone)
                      : null,
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _selectClient,
                ),
              ),

              const SizedBox(height: 16),

              // Location Selection Section
              const Text(
                'Joylashuv tanlang',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 12),

              // Province Dropdown
              _buildProvinceDropdown(state),
              const SizedBox(height: 12),

              // Region Dropdown
              _buildRegionDropdown(state),
              const SizedBox(height: 12),

              // District Dropdown
              _buildDistrictDropdown(state),
            ],
          ),
        );
      },
    );
  }

  Widget _buildProvinceDropdown(OrdersState state) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Viloyat',
                style: TextStyle(fontWeight: FontWeight.w500)),
            const SizedBox(height: 8),
            DropdownButtonFormField<Province>(
              value: _getValidProvince(state),
              decoration: const InputDecoration(
                hintText: 'Viloyatni tanlang',
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: state.provinces?.map((province) {
                    return DropdownMenuItem<Province>(
                      value: province,
                      child: Text(province.title),
                    );
                  }).toList() ??
                  [],
              onChanged: state.isLoadingProvinces
                  ? null
                  : (Province? province) {
                      if (province != null) {
                        context
                            .read<OrdersBloc>()
                            .add(SelectProvince(province));
                        // Save user's province preference
                        GetStorage()
                            .write('user_default_province_id', province.id);
                      }
                    },
              validator: (value) => value == null ? 'Viloyatni tanlang' : null,
            ),
            if (state.isLoadingProvinces)
              const Padding(
                padding: EdgeInsets.only(top: 8),
                child: LinearProgressIndicator(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildRegionDropdown(OrdersState state) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Tuman', style: TextStyle(fontWeight: FontWeight.w500)),
            const SizedBox(height: 8),
            DropdownButtonFormField<Region>(
              value: _getValidRegion(state),
              decoration: const InputDecoration(
                hintText: 'Tumanni tanlang',
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: state.regions?.map((region) {
                    return DropdownMenuItem<Region>(
                      value: region,
                      child: Text(region.title),
                    );
                  }).toList() ??
                  [],
              onChanged: (state.isLoadingRegions ||
                      state.selectedProvince == null)
                  ? null
                  : (Region? region) {
                      if (region != null) {
                        context.read<OrdersBloc>().add(SelectRegion(region));
                        // Save user's region preference
                        GetStorage().write('user_default_region_id', region.id);
                      }
                    },
              validator: (value) => value == null ? 'Tumanni tanlang' : null,
            ),
            if (state.isLoadingRegions)
              const Padding(
                padding: EdgeInsets.only(top: 8),
                child: LinearProgressIndicator(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDistrictDropdown(OrdersState state) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Shahar/Qishloq',
                style: TextStyle(fontWeight: FontWeight.w500)),
            const SizedBox(height: 8),
            DropdownButtonFormField<District>(
              value: _getValidDistrict(state),
              decoration: const InputDecoration(
                hintText: 'Shahar/Qishloqni tanlang',
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: state.districts?.map((district) {
                    return DropdownMenuItem<District>(
                      value: district,
                      child: Text(district.title),
                    );
                  }).toList() ??
                  [],
              onChanged:
                  (state.isLoadingDistricts || state.selectedRegion == null)
                      ? null
                      : (District? district) {
                          if (district != null) {
                            context
                                .read<OrdersBloc>()
                                .add(SelectDistrict(district));
                            // Save user's district preference
                            GetStorage()
                                .write('user_default_district_id', district.id);
                          }
                        },
              validator: (value) =>
                  value == null ? 'Shahar/Qishloqni tanlang' : null,
            ),
            if (state.isLoadingDistricts)
              const Padding(
                padding: EdgeInsets.only(top: 8),
                child: LinearProgressIndicator(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPhotoCapture() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Rasmlar',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const Text(
            'Uy eshigi va daraxt ekiladigan joy rasmlarini oling',
            style: TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: photos.length + (photos.length < 2 ? 1 : 0),
              itemBuilder: (context, index) {
                if (index >= photos.length) {
                  return _buildPhotoPlaceholder();
                }
                return _buildPhotoItem(photos[index], index);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoPlaceholder() {
    return GestureDetector(
      onTap: _capturePhoto,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey, style: BorderStyle.solid),
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.camera_alt, size: 48, color: Colors.grey),
            SizedBox(height: 8),
            Text('Rasm olish', style: TextStyle(color: Colors.grey)),
          ],
        ),
      ),
    );
  }

  Widget _buildPhotoItem(File photo, int index) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Image.file(
            photo,
            width: double.infinity,
            height: double.infinity,
            fit: BoxFit.cover,
          ),
        ),
        Positioned(
          top: 8,
          right: 8,
          child: GestureDetector(
            onTap: () => _removePhoto(index),
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.close, color: Colors.white, size: 16),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTreeSelection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Daraxt turlari',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: treeProducts.length + 1,
              itemBuilder: (context, index) {
                if (index == treeProducts.length) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    child: SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _addTreeProduct,
                        icon: const Icon(Icons.add),
                        label: const Text('Daraxt turi qo\'shish'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green[600],
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.all(16),
                        ),
                      ),
                    ),
                  );
                }
                return TreeProductCard(
                  key: ValueKey('tree_product_${treeProducts[index].treeId}'),
                  product: treeProducts[index],
                  onChanged: (updatedProduct) {
                    setState(() {
                      treeProducts[index] = updatedProduct;
                    });
                  },
                  onRemove: () {
                    setState(() {
                      treeProducts.removeAt(index);
                    });
                  },
                );
              },
            ),
          ),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green[50],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Jami summa:',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                Text(
                  '${_calculateTotalPrice().toStringAsFixed(0)} so\'m',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentSelection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'To\'lov usuli',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Card(
            child: Column(
              children: [
                RadioListTile<PaymentType>(
                  value: PaymentType.cash,
                  groupValue: paymentType,
                  onChanged: (value) => setState(() => paymentType = value),
                  title: const Text('Naqd pul'),
                  secondary: const Icon(Icons.payments, color: Colors.green),
                ),
                RadioListTile<PaymentType>(
                  value: PaymentType.credit,
                  groupValue: paymentType,
                  onChanged: (value) => setState(() => paymentType = value),
                  title: const Text('Nasiya'),
                  secondary:
                      const Icon(Icons.credit_card, color: Colors.orange),
                ),
              ],
            ),
          ),
          if (paymentType == PaymentType.credit) ...[
            const SizedBox(height: 16),
            Card(
              color: Colors.orange[50],
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.credit_card, color: Colors.orange[600]),
                        const SizedBox(width: 8),
                        const Text(
                          'Nasiya to\'lovi',
                          style: TextStyle(
                              fontWeight: FontWeight.bold, fontSize: 16),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'Telefon raqam',
                        prefixText: '+998 ',
                        border: OutlineInputBorder(),
                        filled: true,
                        fillColor: Colors.white,
                      ),
                      keyboardType: TextInputType.phone,
                      onChanged: (value) => setState(() => phoneNumber = value),
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed:
                            (phoneNumber != null && phoneNumber!.isNotEmpty)
                                ? _sendSmsCode
                                : null,
                        icon: const Icon(Icons.sms),
                        label: const Text('SMS kod yuborish'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange[600],
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    if (phoneNumber != null && phoneNumber!.isNotEmpty) ...[
                      const SizedBox(height: 12),
                      TextFormField(
                        decoration: InputDecoration(
                          labelText: 'SMS kod',
                          border: const OutlineInputBorder(),
                          filled: true,
                          fillColor: Colors.white,
                          helperText: 'Test uchun "1111" ni kiriting',
                          helperStyle: TextStyle(color: Colors.blue[600]),
                          suffixIcon: smsCode == '1111'
                              ? Icon(Icons.check_circle,
                                  color: Colors.green[600])
                              : null,
                        ),
                        keyboardType: TextInputType.number,
                        maxLength: 4,
                        onChanged: (value) => setState(() => smsCode = value),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSignatureCapture() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with clear button
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Elektron imzo',
                      style:
                          TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                    ),
                    const Text(
                      'Iltimos, pastdagi maydoncha ichida imzo chizing',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
              OutlinedButton.icon(
                onPressed: () {
                  _signatureController.clear();
                  setState(() => signature = null);
                },
                icon: const Icon(Icons.clear),
                label: const Text('Tozalash'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.red,
                  side: const BorderSide(color: Colors.red),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Signature area
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.cFirstColor, width: 2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(6),
                child: Signature(
                  dynamicPressureSupported: true,
                  controller: _signatureController,
                  backgroundColor: Colors.white,
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Info text
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue[600], size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Imzo avtomatik ravishda saqlanadi. Keyingi bosqichga o\'tish uchun "Keyingisi" tugmasini bosing.',
                    style: TextStyle(
                      color: Colors.blue[700],
                      fontSize: 13,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderSummary() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Buyurtma xulosasi',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: SingleChildScrollView(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildSummaryRow(
                          'Mijoz:', selectedClient?.fullName ?? ''),
                      _buildSummaryRow('Rasmlar:', '${photos.length} ta'),
                      _buildSummaryRow(
                          'Daraxt turlari:', '${treeProducts.length} ta'),
                      _buildSummaryRow('Jami summa:',
                          '${_calculateTotalPrice().toStringAsFixed(0)} so\'m'),
                      _buildSummaryRow('To\'lov usuli:',
                          paymentType == PaymentType.cash ? 'Naqd' : 'Nasiya'),
                      if (signature != null)
                        _buildSummaryRow('Imzo:', 'Mavjud'),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons(OrdersState state) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            offset: const Offset(0, -2),
            blurRadius: 8,
            color: Colors.black.withOpacity(0.1),
          ),
        ],
      ),
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _previousStep,
                child: const Text('Orqaga'),
              ),
            ),
          if (_currentStep > 0) const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: state.isCreating ? null : _nextStep,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green[600],
                foregroundColor: Colors.white,
              ),
              child: state.isCreating
                  ? const CircularProgressIndicator(color: Colors.white)
                  : Text(_currentStep == 5 ? 'Yaratish' : 'Keyingisi'),
            ),
          ),
        ],
      ),
    );
  }

  void _nextStep() {
    if (_validateCurrentStep()) {
      if (_currentStep < 5) {
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      } else {
        _createOrder();
      }
    }
  }

  void _previousStep() {
    _pageController.previousPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  bool _validateCurrentStep() {
    switch (_currentStep) {
      case 0:
        if (selectedClient == null) {
          _showTopSnackBar('Mijoz tanlang');
          return false;
        }

        final state = context.read<OrdersBloc>().state;
        if (state.selectedProvince == null) {
          _showTopSnackBar('Viloyatni tanlang');
          return false;
        }
        if (state.selectedRegion == null) {
          _showTopSnackBar('Tumanni tanlang');
          return false;
        }
        if (state.selectedDistrict == null) {
          _showTopSnackBar('Shahar/Qishloqni tanlang');
          return false;
        }
        return true;
      case 1:
        if (photos.isEmpty) {
          _showTopSnackBar('Kamida 1 ta rasm oling');
          return false;
        }
        return true;
      case 2:
        if (treeProducts.isEmpty) {
          _showTopSnackBar('Kamida 1 ta daraxt turi qo\'shing');
          return false;
        }
        return true;
      case 3:
        if (paymentType == null) {
          _showTopSnackBar('To\'lov usulini tanlang');
          return false;
        }
        if (paymentType == PaymentType.credit) {
          if (phoneNumber == null || phoneNumber!.isEmpty) {
            _showTopSnackBar('Telefon raqamni kiriting');
            return false;
          }
          if (smsCode == null || smsCode!.isEmpty) {
            _showTopSnackBar('SMS kodni kiriting');
            return false;
          }
          if (!_validateSmsCode()) {
            _showTopSnackBar(
                'SMS kod noto\'g\'ri. Test uchun "1111" ni kiriting.');
            return false;
          }
        }
        return true;
      case 4:
        // Automatically capture signature if controller has content
        if (_signatureController.isNotEmpty) {
          _captureSignature();
          return true;
        } else {
          _showTopSnackBar('Imzo chizing');
          return false;
        }
      default:
        return true;
    }
  }

  void _showTopSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(color: Colors.white)),
        behavior: SnackBarBehavior.floating,
        margin: EdgeInsets.only(
          bottom: 80,
          left: 20,
          right: 20,
        ),
        backgroundColor: isError ? Colors.red[600] : AppColors.cGreenishColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  Future<void> _getCurrentLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _showTopSnackBar('Joylashuv xizmati o\'chirilgan. Iltimos yoqing.', isError: true);
        return;
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          _showTopSnackBar('Joylashuv ruxsati berilmagan', isError: true);
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _showTopSnackBar(
            'Joylashuv ruxsati doimiy ravishda rad etilgan. Sozlamalardan yoqing.', isError: true);
        return;
      }

      // Get current position
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );
      setState(() => currentPosition = position);
    } catch (e) {
      print('Error getting location: $e');
      _showTopSnackBar('Joylashuvni olishda xatolik: ${e.toString()}');
    }
  }

  void _selectClient() async {
    final client = await showModalBottomSheet<Customer>(
      context: context,
      isScrollControlled: true,
      builder: (context) => const ClientSelectorBottomSheet(),
    );
    if (client != null) {
      setState(() => selectedClient = client);
      // Set location defaults from selected client
      _setDefaultsFromSelectedClient();
    }
  }

  Future<void> _capturePhoto() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(
      source: ImageSource.camera,
      imageQuality: 85,
    );
    if (image != null) {
      setState(() => photos.add(File(image.path)));
    }
  }

  void _removePhoto(int index) {
    setState(() => photos.removeAt(index));
  }

  void _addTreeProduct() {
    setState(() {
      treeProducts.add(TreeProduct(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        // Generate unique ID
        treeId: '',
        treeType: '',
        variety: '',
        count: 1,
        price: 0,
        totalPrice: 0,
      ));
    });
  }

  double _calculateTotalPrice() {
    return treeProducts.fold(0, (sum, product) => sum + product.totalPrice);
  }

  void _sendSmsCode() {
    if (phoneNumber != null && phoneNumber!.isNotEmpty) {
      // NOTE: Mock SMS sending for testing purposes
      // In production, this would integrate with a real SMS service
      setState(() {
        // Mark that SMS was sent
      });

      _showTopSnackBar('SMS kod yuborildi. Test uchun "1111" ni kiriting.');
    } else {
      _showTopSnackBar('Telefon raqamni kiriting', isError: true);
    }
  }

  bool _validateSmsCode() {
    // NOTE: Mock validation for testing purposes
    // In production, this would validate against the actual SMS code sent
    return smsCode == '1111';
  }

  Future<void> _captureSignature() async {
    if (_signatureController.isNotEmpty) {
      final signatureBytes = await _signatureController.toPngBytes();
      setState(() => signature = signatureBytes);
    }
  }

  Future<void> _createOrder() async {
    if (currentPosition == null) {
      _showTopSnackBar('Joylashuv ma\'lumoti olinmayapti', isError: true);
      return;
    }

    // Generate PDF contract
    // await _generateContract();

    final state = context.read<OrdersBloc>().state;
    final request = CreateOrderRequest(
      client: selectedClient!.id,
      totalPrice: _calculateTotalPrice(),
      paymentType: paymentType == PaymentType.cash ? 1 : 2,
      province: state.selectedProvince?.id ?? '',
      region: state.selectedRegion?.id ?? '',
      district: state.selectedDistrict?.id ?? '',
      location: Location(
        latitude: currentPosition!.latitude.toString(),
        longitude: currentPosition!.longitude.toString(),
      ),
      products: treeProducts,
      photos: photos,
      // contractFile: contractFile,
      signature: signature != null ? base64Encode(signature!) : null,
    );

    context.read<OrdersBloc>().add(CreateOrder(request));
  }

  Future<void> _generateContract() async {
    try {
      final pdf = pw.Document();

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Header
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(20),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.green100,
                    borderRadius: pw.BorderRadius.circular(8),
                  ),
                  child: pw.Column(
                    children: [
                      pw.Text(
                        'DARAXT SOTISH SHARTNOMASI',
                        style: pw.TextStyle(
                          fontSize: 20,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 10),
                      pw.Text(
                        'Shartnoma raqami: ${DateTime.now().millisecondsSinceEpoch}',
                        style: const pw.TextStyle(fontSize: 14),
                      ),
                      pw.Text(
                        'Sana: ${_formatDate(DateTime.now())}',
                        style: const pw.TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 20),

                // Client information
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(15),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColors.grey300),
                    borderRadius: pw.BorderRadius.circular(5),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'MIJOZ MA\'LUMOTLARI',
                        style: pw.TextStyle(
                          fontSize: 16,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 10),
                      pw.Text('F.I.Sh: ${selectedClient!.fullName}'),
                      pw.Text('Telefon: ${selectedClient!.phone}'),
                      pw.Text('Manzil: ${selectedClient!.address}'),
                    ],
                  ),
                ),

                pw.SizedBox(height: 20),

                // Products table
                pw.Container(
                  width: double.infinity,
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'SOTILAYOTGAN DARAXTLAR',
                        style: pw.TextStyle(
                          fontSize: 16,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 10),
                      pw.Table(
                        border: pw.TableBorder.all(),
                        children: [
                          pw.TableRow(
                            children: [
                              pw.Padding(
                                padding: const pw.EdgeInsets.all(8),
                                child: pw.Text('Daraxt turi',
                                    style: pw.TextStyle(
                                        fontWeight: pw.FontWeight.bold)),
                              ),
                              pw.Padding(
                                padding: const pw.EdgeInsets.all(8),
                                child: pw.Text('Nav',
                                    style: pw.TextStyle(
                                        fontWeight: pw.FontWeight.bold)),
                              ),
                              pw.Padding(
                                padding: const pw.EdgeInsets.all(8),
                                child: pw.Text('Soni',
                                    style: pw.TextStyle(
                                        fontWeight: pw.FontWeight.bold)),
                              ),
                              pw.Padding(
                                padding: const pw.EdgeInsets.all(8),
                                child: pw.Text('Narxi',
                                    style: pw.TextStyle(
                                        fontWeight: pw.FontWeight.bold)),
                              ),
                              pw.Padding(
                                padding: const pw.EdgeInsets.all(8),
                                child: pw.Text('Jami',
                                    style: pw.TextStyle(
                                        fontWeight: pw.FontWeight.bold)),
                              ),
                            ],
                          ),
                          ...treeProducts
                              .map((product) => pw.TableRow(
                                    children: [
                                      pw.Padding(
                                        padding: const pw.EdgeInsets.all(8),
                                        child: pw.Text(product.treeType),
                                      ),
                                      pw.Padding(
                                        padding: const pw.EdgeInsets.all(8),
                                        child: pw.Text(product.variety),
                                      ),
                                      pw.Padding(
                                        padding: const pw.EdgeInsets.all(8),
                                        child:
                                            pw.Text(product.count.toString()),
                                      ),
                                      pw.Padding(
                                        padding: const pw.EdgeInsets.all(8),
                                        child: pw.Text(
                                            '${product.price.toStringAsFixed(0)} so\'m'),
                                      ),
                                      pw.Padding(
                                        padding: const pw.EdgeInsets.all(8),
                                        child: pw.Text(
                                            '${product.totalPrice.toStringAsFixed(0)} so\'m'),
                                      ),
                                    ],
                                  ))
                              .toList(),
                        ],
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 20),

                // Total
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(15),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.green50,
                    border: pw.Border.all(color: PdfColors.green300),
                    borderRadius: pw.BorderRadius.circular(5),
                  ),
                  child: pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text(
                        'JAMI SUMMA:',
                        style: pw.TextStyle(
                          fontSize: 18,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.Text(
                        '${_calculateTotalPrice().toStringAsFixed(0)} so\'m',
                        style: pw.TextStyle(
                          fontSize: 18,
                          fontWeight: pw.FontWeight.bold,
                          color: PdfColors.green800,
                        ),
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 20),

                // Payment method
                pw.Container(
                  padding: const pw.EdgeInsets.all(15),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColors.grey300),
                    borderRadius: pw.BorderRadius.circular(5),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'TO\'LOV USULI',
                        style: pw.TextStyle(
                          fontSize: 16,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 5),
                      pw.Text(paymentType == PaymentType.cash
                          ? 'Naqd pul'
                          : 'Nasiya'),
                    ],
                  ),
                ),

                pw.Spacer(),

                // Signature section
                if (signature != null)
                  pw.Container(
                    width: double.infinity,
                    child: pw.Column(
                      children: [
                        pw.Text(
                          'MIJOZ IMZOSI',
                          style: pw.TextStyle(
                            fontSize: 14,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                        pw.SizedBox(height: 10),
                        pw.Container(
                          height: 80,
                          width: 200,
                          decoration: pw.BoxDecoration(
                            border: pw.Border.all(),
                          ),
                          child: pw.Center(
                            child: pw.Image(
                              pw.MemoryImage(signature!),
                              fit: pw.BoxFit.contain,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                pw.SizedBox(height: 20),

                // Footer
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.grey100,
                    borderRadius: pw.BorderRadius.circular(5),
                  ),
                  child: pw.Text(
                    'Ushbu shartnoma ${_formatDate(DateTime.now())} sanada tuzilgan va ikki tomon o\'rtasida kelishilgan.',
                    style: const pw.TextStyle(fontSize: 12),
                    textAlign: pw.TextAlign.center,
                  ),
                ),
              ],
            );
          },
        ),
      );

      final bytes = await pdf.save();
      final tempDir = await getTemporaryDirectory();
      final file = File(
          '${tempDir.path}/contract_${DateTime.now().millisecondsSinceEpoch}.pdf');
      await file.writeAsBytes(bytes);

      setState(() => contractFile = file);
    } catch (e) {
      print('Error generating PDF: $e');
      _showTopSnackBar('Shartnoma yaratishda xatolik', isError: true);
    }
  }

  Province? _getValidProvince(OrdersState state) {
    if (state.selectedProvince == null || state.provinces == null) return null;
    try {
      return state.provinces!
          .firstWhere((p) => p.id == state.selectedProvince!.id);
    } catch (e) {
      return null;
    }
  }

  Region? _getValidRegion(OrdersState state) {
    if (state.selectedRegion == null || state.regions == null) return null;
    try {
      return state.regions!.firstWhere((r) => r.id == state.selectedRegion!.id);
    } catch (e) {
      return null;
    }
  }

  District? _getValidDistrict(OrdersState state) {
    if (state.selectedDistrict == null || state.districts == null) return null;
    try {
      return state.districts!
          .firstWhere((d) => d.id == state.selectedDistrict!.id);
    } catch (e) {
      return null;
    }
  }

  void _setDefaultsFromSelectedClient() {
    if (selectedClient != null) {
      // Set province default from selectedClient
      if (selectedClient!.province != null) {
        context
            .read<OrdersBloc>()
            .add(SelectProvince(selectedClient!.province!));
      }

      // Set region default from selectedClient
      if (selectedClient!.region != null) {
        context.read<OrdersBloc>().add(SelectRegion(selectedClient!.region!));
      }
    }
  }

  @override
  void dispose() {
    _signatureController.dispose();
    _pageController.dispose();
    super.dispose();
  }
}

// Supporting widgets and models
class TreeProductCard extends StatefulWidget {
  final TreeProduct product;
  final Function(TreeProduct) onChanged;
  final VoidCallback onRemove;

  const TreeProductCard({
    super.key,
    required this.product,
    required this.onChanged,
    required this.onRemove,
  });

  @override
  State<TreeProductCard> createState() => _TreeProductCardState();
}

// Isolated dropdown widget for varieties
class _IsolatedVarietyDropdown extends StatefulWidget {
  final String productId;
  final TreeVariety? initialValue;
  final Function(TreeVariety?) onChanged;

  const _IsolatedVarietyDropdown({
    required this.productId,
    this.initialValue,
    required this.onChanged,
  });

  @override
  State<_IsolatedVarietyDropdown> createState() =>
      _IsolatedVarietyDropdownState();
}

class _IsolatedVarietyDropdownState extends State<_IsolatedVarietyDropdown> {
  List<TreeVariety> _varieties = [];
  TreeVariety? _selectedVariety;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _selectedVariety = widget.initialValue;
    _loadVarieties();
  }

  Future<void> _loadVarieties() async {
    if (_varieties.isNotEmpty) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Get varieties from BLoC
      final context = this.context;
      if (context.mounted) {
        final ordersBloc = context.read<OrdersBloc>();
        final response = await ordersBloc.orderRemoteDatasource
            .getAllVarieties(page: 1, limit: 50);

        if (mounted) {
          setState(() {
            _varieties = response.varieties;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _varieties = [];
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<TreeVariety>(
      key: ValueKey('isolated_variety_${widget.productId}'),
      value: _selectedVariety != null &&
              _varieties.any((v) => v.id == _selectedVariety!.id)
          ? _selectedVariety
          : null,
      decoration: const InputDecoration(
        hintText: 'Daraxt navini tanlang',
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      items: _isLoading
          ? []
          : _varieties.map((variety) {
              return DropdownMenuItem<TreeVariety>(
                value: variety,
                child: Text(variety.title),
              );
            }).toList(),
      onChanged: _isLoading
          ? null
          : (TreeVariety? variety) {
              setState(() {
                _selectedVariety = variety;
              });
              widget.onChanged(variety);
            },
    );
  }
}

// Isolated dropdown widget for trees
class _IsolatedTreeDropdown extends StatefulWidget {
  final String productId;
  final String? varietyId;
  final Tree? initialValue;
  final Function(Tree?) onChanged;

  const _IsolatedTreeDropdown({
    required this.productId,
    this.varietyId,
    this.initialValue,
    required this.onChanged,
  });

  @override
  State<_IsolatedTreeDropdown> createState() => _IsolatedTreeDropdownState();
}

class _IsolatedTreeDropdownState extends State<_IsolatedTreeDropdown> {
  List<Tree> _trees = [];
  Tree? _selectedTree;
  bool _isLoading = false;
  String? _currentVarietyId;

  @override
  void initState() {
    super.initState();
    _selectedTree = widget.initialValue;
    _currentVarietyId = widget.varietyId;
    if (widget.varietyId != null) {
      _loadTrees(widget.varietyId!);
    }
  }

  @override
  void didUpdateWidget(_IsolatedTreeDropdown oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.varietyId != oldWidget.varietyId) {
      _currentVarietyId = widget.varietyId;
      if (widget.varietyId != null) {
        _loadTrees(widget.varietyId!);
      } else {
        setState(() {
          _trees = [];
          _selectedTree = null;
        });
        widget.onChanged(null);
      }
    }
  }

  Future<void> _loadTrees(String varietyId) async {
    setState(() {
      _isLoading = true;
      _selectedTree = null; // Clear selection when loading new trees
    });

    try {
      final context = this.context;
      if (context.mounted) {
        final ordersBloc = context.read<OrdersBloc>();
        final response = await ordersBloc.orderRemoteDatasource
            .getAllTrees(variety: varietyId);

        if (mounted && _currentVarietyId == varietyId) {
          setState(() {
            _trees = response.trees;
            _isLoading = false;
            // Auto-select first tree if available
            if (_trees.isNotEmpty) {
              _selectedTree = _trees.first;
              widget.onChanged(_selectedTree);
            }
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _trees = [];
          _isLoading = false;
          _selectedTree = null;
        });
        widget.onChanged(null);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<Tree>(
      key: ValueKey('isolated_tree_${widget.productId}'),
      isExpanded: true,
      value:
          _selectedTree != null && _trees.any((t) => t.id == _selectedTree!.id)
              ? _selectedTree
              : null,
      decoration: const InputDecoration(
        hintText: 'Daraxtni tanlang',
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
      ),
      items: _isLoading
          ? []
          : _trees.map((tree) {
              return DropdownMenuItem<Tree>(
                value: tree,
                child: Text(tree.name,
                    style: const TextStyle(fontWeight: FontWeight.w500)),
              );
            }).toList(),
      onChanged: _isLoading
          ? null
          : (Tree? tree) {
              setState(() {
                _selectedTree = tree;
              });
              widget.onChanged(tree);
            },
    );
  }
}

// Global state for selected trees - each product has its own selected tree
class _GlobalSelectedTrees {
  static Map<String, Tree> _selectedTrees = {};
  static Map<String, TreeVariety> _selectedVarieties = {};

  static Tree? getSelectedTree(String productId) => _selectedTrees[productId];

  static TreeVariety? getSelectedVariety(String productId) =>
      _selectedVarieties[productId];

  static void setSelectedTree(String productId, Tree? tree) {
    if (tree == null) {
      _selectedTrees.remove(productId);
    } else {
      _selectedTrees[productId] = tree;
    }
  }

  static void setSelectedVariety(String productId, TreeVariety? variety) {
    if (variety == null) {
      _selectedVarieties.remove(productId);
    } else {
      _selectedVarieties[productId] = variety;
    }
  }
}

class _TreeProductCardState extends State<TreeProductCard> {
  TreeVariety? selectedVariety;
  Tree? selectedTree;
  String? currentVarietyId;

  @override
  void initState() {
    super.initState();
    // Initialize from existing product data if available
    selectedVariety =
        _GlobalSelectedTrees.getSelectedVariety(widget.product.id);
    selectedTree = _GlobalSelectedTrees.getSelectedTree(widget.product.id);
    currentVarietyId = selectedVariety?.id;
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OrdersBloc, OrdersState>(
      builder: (context, state) {
        // Validate and clean up state if needed
        _validateAndCleanupState(state);
        return KeyboardDismisser(
          child: Card(
            margin: const EdgeInsets.only(bottom: 12),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with delete button
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          selectedTree?.name ?? 'Daraxt tanlang',
                          style: const TextStyle(
                              fontWeight: FontWeight.bold, fontSize: 16),
                        ),
                      ),
                      IconButton(
                        onPressed: widget.onRemove,
                        icon: const Icon(Icons.delete, color: Colors.red),
                      ),
                    ],
                  ),
          
                  const SizedBox(height: 12),
          
                  // Variety Selection
                  const Text('Daraxt turi (nav)',
                      style: TextStyle(fontWeight: FontWeight.w500)),
                  const SizedBox(height: 8),
                  _IsolatedVarietyDropdown(
                    productId: widget.product.id,
                    initialValue: selectedVariety,
                    onChanged: (TreeVariety? variety) {
                      setState(() {
                        selectedVariety = variety;
                        currentVarietyId = variety?.id;
                        selectedTree =
                            null; // Clear tree selection when variety changes
                      });
          
                      // Update global state
                      _GlobalSelectedTrees.setSelectedVariety(
                          widget.product.id, variety);
                      _GlobalSelectedTrees.setSelectedTree(
                          widget.product.id, null);
          
                      print(
                          'TreeProductCard: Variety selected: ${variety?.title} (${variety?.id})');
          
                      // Update product
                      _updateProduct();
                    },
                  ),
          
                  const SizedBox(height: 12),
          
                  // Tree Selection
                  const Text('Daraxt',
                      style: TextStyle(fontWeight: FontWeight.w500)),
                  const SizedBox(height: 8),
                  _IsolatedTreeDropdown(
                    productId: widget.product.id,
                    varietyId: currentVarietyId,
                    initialValue: selectedTree,
                    onChanged: (Tree? tree) {
                      setState(() {
                        selectedTree = tree;
                      });
          
                      // Update global state
                      _GlobalSelectedTrees.setSelectedTree(
                          widget.product.id, tree);
          
                      if (tree != null) {
                        print(
                            'TreeProductCard: Selected tree ${tree.name} (${tree.id})');
                      }
          
                      // Update product
                      _updateProduct();
                    },
                  ),
          
                  const SizedBox(height: 12),
          
                  // Quantity and Price
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          key: ValueKey('quantity_${widget.product.id}'),
                          decoration: InputDecoration(
                            labelText: 'Soni',
                            border: const OutlineInputBorder(),
                            helperText: selectedTree != null &&
                                    selectedTree!.count != null &&
                                    selectedTree!.count! > 0
                                ? '- Mavjud: ${selectedTree!.count} dona'
                                : selectedTree != null
                                    ? '- Cheksiz mavjud'
                                    : '- Daraxtni tanlang',
                          ),
                          keyboardType: TextInputType.number,
                          initialValue: widget.product.count.toString(),
                          validator: (value) {
                            final count = int.tryParse(value ?? '') ?? 0;
                            if (count <= 0) {
                              return 'Soni 0 dan katta bo\'lishi kerak';
                            }
                            // Only validate against stock if tree has limited stock (count > 0)
                            if (selectedTree != null &&
                                selectedTree!.count != null &&
                                selectedTree!.count! > 0 &&
                                count > selectedTree!.count!) {
                              return 'Mavjud miqdordan ko\'p: ${selectedTree!.count}';
                            }
                            return null;
                          },
                          onChanged: (value) {
                            final count = int.tryParse(value) ?? 1;
                            // Limit to available stock only if tree has limited stock
                            final limitedCount = selectedTree != null &&
                                    selectedTree!.count != null &&
                                    selectedTree!.count! > 0
                                ? count.clamp(1, selectedTree!.count!)
                                : count.clamp(1,
                                    999999); // Allow large quantities for unlimited stock
                            _updateProduct(count: limitedCount);
                          },
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: TextFormField(
                          key: ValueKey(selectedTree?.id),
                          decoration: const InputDecoration(
                            labelText: 'Birlik narxi',
                            helperText: '- So\'m',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          readOnly: true,
                          // Auto-calculated from selected tree
                          initialValue: selectedTree?.price.toStringAsFixed(0) ??
                              widget.product.price.toString(),
                        ),
                      ),
                    ],
                  ),
          
                  const SizedBox(height: 8),
          
                  // Total Price
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      'Jami: ${widget.product.totalPrice.toStringAsFixed(0)} so\'m',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                      textAlign: TextAlign.right,
                    ),
                  ),
          
                  // Loading indicators
                  if (state.isLoadingVarieties)
                    const Padding(
                      padding: EdgeInsets.only(top: 8),
                      child: LinearProgressIndicator(),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// Validate and cleanup state if selections are no longer valid
  void _validateAndCleanupState(OrdersState state) {
    // Since we're using isolated dropdowns, minimal validation is needed
    // The isolated dropdowns handle their own state validation
  }

  void _updateProduct({int? count, double? price}) {
    // Use the selected tree's unit price if available
    final unitPrice = selectedTree?.price ?? price ?? widget.product.price;
    final quantity = count ?? widget.product.count;

    final updatedProduct = widget.product.copyWith(
      treeId: selectedTree?.id ?? widget.product.treeId,
      treeType: selectedTree?.name ?? widget.product.treeType,
      variety: selectedVariety?.title ?? widget.product.variety,
      count: quantity,
      price: unitPrice,
      totalPrice: quantity * unitPrice,
    );
    widget.onChanged(updatedProduct);
  }
}

enum PaymentType { cash, credit }

class TreeProduct {
  final String id;
  final String treeId;
  final String treeType;
  final String variety;
  final int count;
  final double price;
  final double totalPrice;

  TreeProduct({
    required this.id,
    required this.treeId,
    required this.treeType,
    required this.variety,
    required this.count,
    required this.price,
    required this.totalPrice,
  });

  TreeProduct copyWith({
    String? id,
    String? treeId,
    String? treeType,
    String? variety,
    int? count,
    double? price,
    double? totalPrice,
  }) {
    return TreeProduct(
      id: id ?? this.id,
      treeId: treeId ?? this.treeId,
      treeType: treeType ?? this.treeType,
      variety: variety ?? this.variety,
      count: count ?? this.count,
      price: price ?? this.price,
      totalPrice: totalPrice ?? this.totalPrice,
    );
  }
}

// Client Selector Bottom Sheet
class ClientSelectorBottomSheet extends StatefulWidget {
  const ClientSelectorBottomSheet({super.key});

  @override
  State<ClientSelectorBottomSheet> createState() =>
      _ClientSelectorBottomSheetState();
}

class _ClientSelectorBottomSheetState extends State<ClientSelectorBottomSheet> {
  final TextEditingController _searchController = TextEditingController();
  final CustomerService _customerService = CustomerService(
    dio: di.di(),
    storage: di.di(),
    networkInfo: di.di(),
  );

  List<Customer> clients = [];
  bool isLoading = false;
  String? searchQuery;

  @override
  void initState() {
    super.initState();
    _loadClients();
    _searchController.addListener(() {
      if (_searchController.text != searchQuery) {
        searchQuery = _searchController.text;
        _loadClients();
      }
    });
  }

  Future<void> _loadClients() async {
    setState(() => isLoading = true);

    try {
      final response = await _customerService.getCustomers(
        page: 1,
        limit: 50, // Load more clients for selection
        search: searchQuery?.isNotEmpty == true ? searchQuery : null,
        forceRefresh: false,
      );

      setState(() {
        clients = response?.docs ?? [];
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
        clients = [];
      });

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Mijozlarni yuklashda xatolik: $e',
                style: const TextStyle(color: Colors.white)),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.only(
              bottom: MediaQuery.of(context).size.height - 150,
              left: 20,
              right: 20,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.symmetric(vertical: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Title
          const Padding(
            padding: EdgeInsets.all(16),
            child: Text(
              'Mijoz tanlang',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
          ),

          // Search field
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'Mijoz qidirish...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Clients list
          Expanded(
            child: isLoading
                ? const Center(child: CircularProgressIndicator())
                : ListView.builder(
                    shrinkWrap: true,
                    itemCount: clients.length,
                    itemBuilder: (context, index) {
                      final client = clients[index];
                      return ListTile(
                        leading: CircleAvatar(
                          radius: 30,
                          backgroundColor: Colors.green[100],
                          child: Text(
                            //Only first letter
                            client.fullName[0].toUpperCase(),
                            style: TextStyle(color: Colors.green[600]),
                          ),
                        ),
                        title: Text(client.fullName),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(client.phone),
                            Text(
                              client.address,
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                        onTap: () => Navigator.pop(context, client),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}

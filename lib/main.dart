import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_storage/get_storage.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:mobil_kochat/features/order/presentation/bloc/orders_bloc/orders_bloc.dart';
import 'core/theme/app_theme.dart';
import 'core/theme/theme_manager.dart';
import 'core/widgets/smooth_theme_transition.dart';
import 'core/utils/app_constants.dart';
import 'core/utils/jwt_decoder.dart';

import 'core/services/logout_service.dart';
import 'core/services/navigation_service.dart';
import 'features/auth/presentation/bloc/login_bloc/login_bloc.dart';
import 'features/auth/presentation/pages/login_page.dart';
import 'features/customer/presentation/page/costumer_page.dart';
import 'features/splash/splash_screen.dart';

import 'di/dependency_injection.dart' as di;
import 'core/services/language_service.dart';
import 'env.dart';
import 'navigation_page.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations to portrait only
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  await EasyLocalization.ensureInitialized();

  // Initialize environment
  AppEnvironment.setupEnv(Environment.prod);
  await di.init();

  // Get saved language or default to Uzbek
  final savedLanguage = LanguageService.getCurrentLanguage();

  runApp(
    EasyLocalization(
      supportedLocales: const [
        Locale('uz'),
        Locale('ru'),
      ],
      path: 'assets/translations',
      fallbackLocale: const Locale('uz'),
      startLocale: Locale(savedLanguage),
      child: const MobilKochatApp(),
    ),
  );
}

class MobilKochatApp extends StatefulWidget {
  const MobilKochatApp({super.key});

  @override
  State<MobilKochatApp> createState() => _MobilKochatAppState();
}

class _MobilKochatAppState extends State<MobilKochatApp> {
  final GetStorage storage = di.di();
  String lan = "uz";

  @override
  void initState() {
    storage.write(baseUrlPref, AppEnvironment.baseApiUrl);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    //Add BLOC global providers here

    return MultiBlocProvider(
      providers: [
        BlocProvider<LoginBloc>(create: (context) => di.di<LoginBloc>()),
        BlocProvider<OrdersBloc>(create: (context) => di.di<OrdersBloc>()),
      ],
      child: ScreenUtilInit(
        designSize: const Size(390, 844),
        minTextAdapt: true,
        splitScreenMode: true,
        // Use builder only if you need to use library outside ScreenUtilInit context
        builder: (_, child) {
          return AnimatedBuilder(
            animation: ThemeManager(),
            builder: (context, child) {
              return MaterialApp(
                title: 'Mobil Ko\'chat',
                debugShowCheckedModeBanner: false,
                navigatorKey: NavigationService.navigatorKey,
                theme: AppTheme.lightTheme,
                darkTheme: AppTheme.darkTheme,
                themeMode: ThemeManager().themeMode,
                locale: context.locale,
                supportedLocales: context.supportedLocales,
                localizationsDelegates: context.localizationDelegates,
                home: EnhancedThemeTransition(
                  theme: ThemeManager().currentTheme,
                  duration: const Duration(milliseconds: 400),
                  useScaleEffect: true,
                  useFadeEffect: true,
                  child: const AuthWrapper(),
                ),
              );
            },
          );
        },
      ),
    );
  }
}

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  late final GetStorage _storage;
  bool _isLoading = true;
  bool _isAuthenticated = false;

  // Track when splash screen started and minimum display time
  late final DateTime _splashStartTime;
  static const Duration _minimumSplashDuration = Duration(milliseconds: 5000);

  @override
  void initState() {
    super.initState();
    _splashStartTime = DateTime.now();
    _storage = di.di<GetStorage>();
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    // Perform authentication check first (this may complete quickly)
    await _performAuthenticationCheck();

    // Ensure splash screen shows for minimum 3 seconds regardless of auth speed
    await _ensureMinimumSplashDuration();
  }

  Future<void> _performAuthenticationCheck() async {
    final token = _storage.read(TOKEN);
    bool isAuthenticated = false;

    if (token != null) {
      // Check if token is expired
      if (!JwtDecoder.isTokenExpired(token)) {
        isAuthenticated = true;
      } else {
        // Token expired, clear all storage using LogoutService
        final logoutService = LogoutService(storage: _storage, prefs: di.di());
        await logoutService.performCompleteLogout();
      }
    }

    // Store the authentication results but don't update loading state yet
    _isAuthenticated = isAuthenticated;
  }

  Future<void> _ensureMinimumSplashDuration() async {
    final elapsedTime = DateTime.now().difference(_splashStartTime);
    final remainingTime = _minimumSplashDuration - elapsedTime;

    if (remainingTime > Duration.zero) {
      // Wait for the remaining time to ensure minimum splash duration
      await Future.delayed(remainingTime);
    }

    // Now update the loading state to hide splash screen
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const SplashScreen();
    }

    if (_isAuthenticated) {
      return NavigationPage();
    }

    return const LoginPage();
  }
}

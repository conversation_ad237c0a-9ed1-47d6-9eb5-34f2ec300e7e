name: mobil_kochat
description: "Mobile Tree Planting Worker App - Mobil Ko'chat"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.3+3

environment:
  sdk: ^3.2.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  internet_connection_checker: 3.0.0
  http_interceptor: ^2.0.0
  dio: ^5.8.0+1
  get_it: ^8.0.3
  get_storage: ^2.1.1
  flutter_secure_storage: ^9.2.4
  package_info_plus: ^8.3.0
  device_info_plus: ^11.3.0
  in_app_update: ^4.2.3
  flutter_svg: ^2.2.0
  flutter_screenutil: ^5.9.3
  gap: ^3.0.1
  image_picker: ^1.1.2
  dotted_border: ^2.1.0
  pin_code_fields: ^8.0.1
  screen_brightness: ^2.1.5
  flutter_launcher_icons: ^0.14.4
  flutter_native_splash: ^2.4.4
  vibration: ^3.1.3
  lottie: ^3.3.1
  firebase_core: ^3.15.1
  flutter_bloc: ^9.1.1
  auto_sms_verification: ^0.0.8
  fluttertoast: ^8.2.12
  json_annotation: ^4.9.0
  uzpay: ^0.0.3+2
  # Face control dependencies
  settings_ui: ^2.0.2
  flutter_session_manager: ^1.0.3
  ntp: ^2.0.0
  pausable_timer: ^3.1.0+3
  native_device_orientation: ^1.1.4
  permission_handler: ^11.3.1
  sqflite: ^2.4.1
  path_provider: ^2.1.4
  isar: ^3.1.0+1
  isar_flutter_libs: ^3.1.0+1
  flutter_animate: ^4.5.0
  zoom_tap_animation: ^1.1.0
  http_parser: ^4.1.1
  awesome_notifications: ^0.10.1
  equatable: 2.0.7
  intl: ^0.20.2
  # Camera and image processing packages
  camera: ^0.10.5+9
  flutter_image_compress: ^2.3.0
  gal: ^2.3.0
  image_editor: ^1.4.1
  mno_zoom_widget: ^0.1.0
  shared_preferences: ^2.3.2
  video_compress: ^3.1.3
  # facesdk_plugin: ^2.0.0  # Custom plugin - needs to be added manually
  geolocator: ^12.0.0
  # For FaceControl
  facesdk_plugin:
    path: ./facesdk_plugin
  page_transition: ^2.1.0
  easy_localization: ^3.0.3
  pdf: ^3.11.3
  signature: ^6.3.0
  cached_network_image: ^3.4.1
  keyboard_dismisser: ^3.0.0

dependency_overrides:
  intl: ^0.19.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  build_runner: ^2.4.13
  json_serializable: ^6.8.0
  isar_generator: ^3.1.0+1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets configuration
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/
    - assets/translations/
    - assets/example_data/
    - assets/

  # Custom fonts configuration
  fonts:
    - family: EuclidCircularA
      fonts:
        - asset: assets/fonts/EuclidCircularARegular.ttf
          weight: 400
        - asset: assets/fonts/EuclidCircularAItalic.ttf
          weight: 400
          style: italic
        - asset: assets/fonts/EuclidCircularALight.ttf
          weight: 300
        - asset: assets/fonts/EuclidCircularALightItalic.ttf
          weight: 300
          style: italic
        - asset: assets/fonts/EuclidCircularAMedium.ttf
          weight: 500
        - asset: assets/fonts/EuclidCircularAMediumItalic.ttf
          weight: 500
          style: italic
        - asset: assets/fonts/EuclidCircularASemiBold.ttf
          weight: 600
        - asset: assets/fonts/EuclidCircularASemiBoldItalic.ttf
          weight: 600
          style: italic
        - asset: assets/fonts/EuclidCircularABold.ttf
          weight: 700
        - asset: assets/fonts/EuclidCircularABoldItalic.ttf
          weight: 700
          style: italic
            # Language generate
          #       flutter pub run easy_localization:generate -S "assets/translations" -O "lib/translations"
          #       flutter pub run easy_localization:generate -S "assets/translations" -O "lib/translations" -o "locale_keys.g.dart" -f  keys
